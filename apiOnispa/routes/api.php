<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CertificateController;


Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');


Route::post('/certificates/fetch', [CertificateController::class, 'fetchAndStore']);

Route::get('/certificates/products', [CertificateController::class, 'getCertificateProducts']);

// New endpoint for inspection form mapping
Route::get('/certificates/inspection-form', [CertificateController::class, 'getCertificateForInspectionForm']);

// New endpoint for testing complete certificate retrieval methods
Route::get('/certificates/test-complete', [CertificateController::class, 'testCompleteCertificateRetrieval']);