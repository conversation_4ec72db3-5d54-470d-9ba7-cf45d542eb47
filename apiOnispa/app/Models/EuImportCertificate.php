<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class EuImportCertificate extends Model
{
    use HasFactory;

    protected $fillable = [
        'import_id',
        'issue_date_time',
        'type_code',
        'purpose_code',
        'status',
        'status_name',
        'local_id',
        'central_competent_authority_code',
        'central_competent_authority_unlocode',
        'local_competent_authority_code',
        'local_competent_authority_unlocode',
        'country_of_issuance',
        'country_of_consignor',
        'consignor_name',
        'country_of_consignee',
        'consignee_name',
        'country_of_origin',
        'country_of_destination',
        'country_of_dispatch',
        'country_of_place_of_loading',
        'country_of_transit',
        'create_date_time',
        'update_date_time',
        'status_change_date_time',
        'declaration_date_time',
        'certification_date_time',
        'has_controls',
        'raw_data',
        'xml_file_path',
    ];

    protected $casts = [
        'raw_data' => 'array',
        'country_of_origin' => 'array',
        'country_of_transit' => 'array',
        'issue_date_time' => 'datetime',
        'create_date_time' => 'datetime',
        'update_date_time' => 'datetime',
        'status_change_date_time' => 'datetime',
        'declaration_date_time' => 'datetime',
        'certification_date_time' => 'datetime',
        'has_controls' => 'boolean',
    ];

    /**
     * Get the consignments for this certificate
     * TODO: Create EuImportConsignment model
     */
    // public function consignments(): HasMany
    // {
    //     return $this->hasMany(EuImportConsignment::class, 'import_id', 'import_id');
    // }

    /**
     * Get the parties for this certificate
     * TODO: Create EuImportParty model
     */
    // public function parties(): HasMany
    // {
    //     return $this->hasMany(EuImportParty::class, 'import_id', 'import_id');
    // }

    /**
     * Get the locations for this certificate
     * TODO: Create EuImportLocation model
     */
    // public function locations(): HasMany
    // {
    //     return $this->hasMany(EuImportLocation::class, 'import_id', 'import_id');
    // }

    /**
     * Get the referenced documents for this certificate
     * TODO: Create EuImportReferencedDocument model
     */
    // public function referencedDocuments(): HasMany
    // {
    //     return $this->hasMany(EuImportReferencedDocument::class, 'import_id', 'import_id');
    // }

    /**
     * Get the authentication records for this certificate
     * TODO: Create EuImportAuthentication model
     */
    // public function authentications(): HasMany
    // {
    //     return $this->hasMany(EuImportAuthentication::class, 'import_id', 'import_id');
    // }

    /**
     * Get the commodity classifications for this certificate
     */
    public function commodities(): HasMany
    {
        return $this->hasMany(EuImportCommodity::class, 'import_id', 'import_id');
    }

    /**
     * Find certificate by import reference
     */
    public static function findByImportReference(string $importReference)
    {
        return static::where('import_id', $importReference)->first();
    }

    /**
     * Search certificates by various criteria
     */
    public static function searchByCriteria(array $criteria)
    {
        $query = static::query();

        if (isset($criteria['status'])) {
            if (is_array($criteria['status'])) {
                $query->whereIn('status', $criteria['status']);
            } else {
                $query->where('status', $criteria['status']);
            }
        }

        if (isset($criteria['local_id'])) {
            $query->where('local_id', 'like', '%' . $criteria['local_id'] . '%');
        }

        if (isset($criteria['country_of_issuance'])) {
            $query->where('country_of_issuance', $criteria['country_of_issuance']);
        }

        if (isset($criteria['country_of_consignor'])) {
            $query->where('country_of_consignor', $criteria['country_of_consignor']);
        }

        if (isset($criteria['country_of_consignee'])) {
            $query->where('country_of_consignee', $criteria['country_of_consignee']);
        }

        if (isset($criteria['country_of_dispatch'])) {
            $query->where('country_of_dispatch', $criteria['country_of_dispatch']);
        }

        if (isset($criteria['consignor_name'])) {
            $query->where('consignor_name', 'like', '%' . $criteria['consignor_name'] . '%');
        }

        if (isset($criteria['consignee_name'])) {
            $query->where('consignee_name', 'like', '%' . $criteria['consignee_name'] . '%');
        }

        // Date range filters
        if (isset($criteria['create_date_from'])) {
            $query->where('create_date_time', '>=', $criteria['create_date_from']);
        }

        if (isset($criteria['create_date_to'])) {
            $query->where('create_date_time', '<=', $criteria['create_date_to']);
        }

        if (isset($criteria['update_date_from'])) {
            $query->where('update_date_time', '>=', $criteria['update_date_from']);
        }

        if (isset($criteria['update_date_to'])) {
            $query->where('update_date_time', '<=', $criteria['update_date_to']);
        }

        if (isset($criteria['certification_date_from'])) {
            $query->where('certification_date_time', '>=', $criteria['certification_date_from']);
        }

        if (isset($criteria['certification_date_to'])) {
            $query->where('certification_date_time', '<=', $criteria['certification_date_to']);
        }

        return $query;
    }

    /**
     * Get the status name in a human-readable format
     */
    public function getStatusNameAttribute($value)
    {
        if ($value) {
            return $value;
        }

        // Map status codes to names if not provided
        $statusMap = [
            47 => 'Draft',
            1 => 'New',
            42 => 'In progress',
            70 => 'Validated',
            41 => 'Rejected',
            55 => 'Deleted',
            64 => 'Cancelled',
            44 => 'Replaced'
        ];

        return $statusMap[$this->status] ?? 'Unknown';
    }

    /**
     * Check if certificate has accompanying documents
     * TODO: Implement when EuImportReferencedDocument model is created
     */
    public function hasAccompanyingDocuments()
    {
        // return $this->referencedDocuments()
        //     ->whereNotNull('attachment_uri')
        //     ->exists();
        return false; // Placeholder
    }

    /**
     * Get accompanying documents
     * TODO: Implement when EuImportReferencedDocument model is created
     */
    public function getAccompanyingDocuments()
    {
        // return $this->referencedDocuments()
        //     ->whereNotNull('attachment_uri')
        //     ->get();
        return collect(); // Placeholder
    }

    /**
     * Save XML response to file
     */
    public function saveXmlFile($xmlContent)
    {
        $filename = 'eu_import_' . $this->import_id . '_' . date('Y-m-d_H-i-s') . '.xml';
        $storagePath = storage_path('app/eu_import_certificates');
        
        // Create directory if it doesn't exist
        if (!file_exists($storagePath)) {
            mkdir($storagePath, 0755, true);
        }
        
        $fullPath = $storagePath . '/' . $filename;
        file_put_contents($fullPath, $xmlContent);
        
        // Update the model with the file path
        $this->xml_file_path = $fullPath;
        $this->save();
        
        return $fullPath;
    }

    /**
     * Get the XML file content
     */
    public function getXmlContent()
    {
        if ($this->xml_file_path && file_exists($this->xml_file_path)) {
            return file_get_contents($this->xml_file_path);
        }
        
        return null;
    }
}
