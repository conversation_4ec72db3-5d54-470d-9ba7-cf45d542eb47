<?php

namespace App\Http\Controllers;

use App\Models\EuImportCertificate;
use App\Models\EuImportCommodity;
use App\Providers\TracesNtEuImportClient;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Exception;

class EuImportController extends Controller
{
    /**
     * Retrieve EU-Import certificate by reference from TRACES API and store it
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function fetchAndStore(Request $request): JsonResponse
    {
        $request->validate([
            'reference' => 'required|string|max:50',
            'save_xml' => 'boolean',
            'force_refresh' => 'boolean'
        ]);

        $reference = $request->input('reference');
        $saveXml = $request->input('save_xml', true);
        $forceRefresh = $request->input('force_refresh', false);

        try {
            // Check if certificate already exists in database
            if (!$forceRefresh) {
                $existingCertificate = EuImportCertificate::findByImportReference($reference);
                if ($existingCertificate) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Certificate found in database',
                        'source' => 'database',
                        'certificate' => $existingCertificate->load(['commodities']),
                        'xml_file_path' => $existingCertificate->xml_file_path
                    ]);
                }
            }

            // Fetch from TRACES API
            $certificateData = $this->fetchCertificateFromApi($reference, $saveXml);

            if (!$certificateData) {
                return response()->json([
                    'success' => false,
                    'message' => 'Certificate not found in TRACES API',
                    'reference' => $reference
                ], 404);
            }

            // Store in database
            $certificate = $this->storeCertificateInDatabase($certificateData, $reference);

            return response()->json([
                'success' => true,
                'message' => 'Certificate retrieved and stored successfully',
                'source' => 'traces_api',
                'certificate' => $certificate->load(['commodities']),
                'xml_file_path' => $certificate->xml_file_path
            ]);

        } catch (Exception $e) {
            \Log::error('EU-Import certificate fetch error', [
                'reference' => $reference,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve certificate: ' . $e->getMessage(),
                'reference' => $reference
            ], 500);
        }
    }

    /**
     * Get EU-Import certificate PDF
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getPdf(Request $request): JsonResponse
    {
        $request->validate([
            'reference' => 'required|string|max:50',
            'extra_languages' => 'array',
            'extra_languages.*' => 'string|size:2'
        ]);

        $reference = $request->input('reference');
        $extraLanguages = $request->input('extra_languages', []);

        try {
            // Get TRACES API configuration
            $tracesConfig = config('services.traces', []);
            if (empty($tracesConfig['username']) || empty($tracesConfig['auth_key'])) {
                throw new Exception('TRACES API configuration not found');
            }

            // Create TracesNtEuImportClient
            $client = new TracesNtEuImportClient(
                $tracesConfig['username'],
                $tracesConfig['auth_key'],
                $tracesConfig['client_id'],
                $tracesConfig['use_production']
            );

            $pdfContent = $client->getEuImportPdfCertificate($reference, $extraLanguages);

            return response()->json([
                'success' => true,
                'message' => 'PDF retrieved successfully',
                'reference' => $reference,
                'pdf_content' => $pdfContent,
                'content_type' => 'application/pdf',
                'encoding' => 'base64'
            ]);

        } catch (Exception $e) {
            \Log::error('EU-Import PDF fetch error', [
                'reference' => $reference,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve PDF: ' . $e->getMessage(),
                'reference' => $reference
            ], 500);
        }
    }

    /**
     * Get EU-Import certificate signed PDF
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getSignedPdf(Request $request): JsonResponse
    {
        $request->validate([
            'reference' => 'required|string|max:50'
        ]);

        $reference = $request->input('reference');

        try {
            // Get TRACES API configuration
            $tracesConfig = config('services.traces', []);
            if (empty($tracesConfig['username']) || empty($tracesConfig['auth_key'])) {
                throw new Exception('TRACES API configuration not found');
            }

            // Create TracesNtEuImportClient
            $client = new TracesNtEuImportClient(
                $tracesConfig['username'],
                $tracesConfig['auth_key'],
                $tracesConfig['client_id'],
                $tracesConfig['use_production']
            );

            $pdfContent = $client->getEuImportSignedPdfCertificate($reference);

            return response()->json([
                'success' => true,
                'message' => 'Signed PDF retrieved successfully',
                'reference' => $reference,
                'pdf_content' => $pdfContent,
                'content_type' => 'application/pdf',
                'encoding' => 'base64',
                'signed' => true
            ]);

        } catch (Exception $e) {
            \Log::error('EU-Import Signed PDF fetch error', [
                'reference' => $reference,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve signed PDF: ' . $e->getMessage(),
                'reference' => $reference
            ], 500);
        }
    }

    /**
     * Search EU-Import certificates
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function search(Request $request): JsonResponse
    {
        $request->validate([
            'page_size' => 'integer|min:1|max:200',
            'offset' => 'integer|min:0',
            'status' => 'array',
            'status.*' => 'integer',
            'local_id' => 'string|max:100',
            'cn_code' => 'array',
            'cn_code.*' => 'string|max:20',
            'cn_code_exact_match' => 'boolean',
            'country_of_issuance' => 'string|size:2',
            'country_of_consignor' => 'string|size:2',
            'country_of_consignee' => 'string|size:2',
            'country_of_dispatch' => 'string|size:2',
            'create_date_from' => 'date',
            'create_date_to' => 'date',
            'update_date_from' => 'date',
            'update_date_to' => 'date',
            'certification_date_from' => 'date',
            'certification_date_to' => 'date',
            'use_api' => 'boolean'
        ]);

        $useApi = $request->input('use_api', false);

        try {
            if ($useApi) {
                // Search using TRACES API
                return $this->searchUsingApi($request);
            } else {
                // Search in local database
                return $this->searchInDatabase($request);
            }

        } catch (Exception $e) {
            \Log::error('EU-Import search error', [
                'criteria' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Search failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get certificate commodities
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getCommodities(Request $request): JsonResponse
    {
        $request->validate([
            'reference' => 'required|string|max:50'
        ]);

        $reference = $request->input('reference');

        try {
            $commodities = EuImportCommodity::getByImportReference($reference);

            return response()->json([
                'success' => true,
                'reference' => $reference,
                'commodities' => $commodities
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve commodities: ' . $e->getMessage(),
                'reference' => $reference
            ], 500);
        }
    }

    /**
     * Fetch certificate from TRACES API
     */
    private function fetchCertificateFromApi(string $reference, bool $saveXml = true): ?array
    {
        // Get TRACES API configuration
        $tracesConfig = config('services.traces', []);
        if (empty($tracesConfig['username']) || empty($tracesConfig['auth_key'])) {
            throw new Exception('TRACES API configuration not found');
        }

        // Create TracesNtEuImportClient
        $client = new TracesNtEuImportClient(
            $tracesConfig['username'],
            $tracesConfig['auth_key'],
            $tracesConfig['client_id'],
            $tracesConfig['use_production']
        );

        \Log::info('Fetching EU-Import certificate from TRACES API', [
            'reference' => $reference
        ]);

        try {
            $certificateData = $client->getEuImportCertificate($reference);

            if ($saveXml && isset($certificateData['raw_xml'])) {
                $client->saveXmlResponse($certificateData['raw_xml'], 'eu_import_' . $reference . '.xml');
            }

            return $certificateData;

        } catch (Exception $e) {
            \Log::error('Failed to fetch EU-Import certificate from API', [
                'reference' => $reference,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Store certificate data in database
     */
    private function storeCertificateInDatabase(array $certificateData, string $reference): EuImportCertificate
    {
        \Log::info('Storing EU-Import certificate in database', [
            'reference' => $reference
        ]);

        // Create or update the main certificate record
        $certificate = EuImportCertificate::updateOrCreate(
            ['import_id' => $reference],
            [
                'issue_date_time' => $certificateData['issue_date_time'] ?? null,
                'type_code' => $certificateData['type_code'] ?? null,
                'purpose_code' => $certificateData['purpose_code'] ?? null,
                'status' => $this->extractStatusFromData($certificateData),
                'status_name' => $this->extractStatusNameFromData($certificateData),
                'local_id' => $this->extractLocalIdFromData($certificateData),
                'country_of_issuance' => $this->extractCountryFromParties($certificateData, 'issuance'),
                'country_of_consignor' => $this->extractCountryFromParties($certificateData, 'consignor'),
                'consignor_name' => $this->extractPartyName($certificateData, 'ConsignorSPSParty'),
                'country_of_consignee' => $this->extractCountryFromParties($certificateData, 'consignee'),
                'consignee_name' => $this->extractPartyName($certificateData, 'ConsigneeSPSParty'),
                'country_of_origin' => $this->extractCountriesOfOrigin($certificateData),
                'country_of_destination' => $this->extractCountryFromLocations($certificateData, 'destination'),
                'country_of_dispatch' => $this->extractCountryFromLocations($certificateData, 'dispatch'),
                'country_of_place_of_loading' => $this->extractCountryFromLocations($certificateData, 'loading'),
                'country_of_transit' => $this->extractCountriesOfTransit($certificateData),
                'create_date_time' => now(), // Set to current time for new records
                'update_date_time' => now(),
                'certification_date_time' => $this->extractCertificationDateTime($certificateData),
                'has_controls' => $this->checkHasControls($certificateData),
                'raw_data' => $certificateData,
            ]
        );

        // Store commodities
        $this->storeCommodities($certificate, $certificateData);

        // Save XML file if available
        if (isset($certificateData['raw_xml'])) {
            $certificate->saveXmlFile($certificateData['raw_xml']);
        }

        return $certificate;
    }

    /**
     * Store commodity data
     */
    private function storeCommodities(EuImportCertificate $certificate, array $certificateData): void
    {
        // Clear existing commodities
        EuImportCommodity::where('import_id', $certificate->import_id)->delete();

        if (!isset($certificateData['consignments'])) {
            return;
        }

        foreach ($certificateData['consignments'] as $consignment) {
            if (!isset($consignment['consignment_items'])) {
                continue;
            }

            foreach ($consignment['consignment_items'] as $item) {
                if (!isset($item['nature_identification'])) {
                    continue;
                }

                foreach ($item['nature_identification'] as $nature) {
                    EuImportCommodity::create([
                        'import_id' => $certificate->import_id,
                        'system_id' => $nature['system_id'] ?? null,
                        'system_name' => $nature['system_name'] ?? null,
                        'class_code' => $nature['class_code'] ?? null,
                        'class_name' => $nature['class_name'] ?? null,
                        'sequence_numeric' => $item['sequence_numeric'] ?? null,
                    ]);
                }
            }
        }
    }

    /**
     * Search using TRACES API
     */
    private function searchUsingApi(Request $request): JsonResponse
    {
        // Get TRACES API configuration
        $tracesConfig = config('services.traces', []);
        if (empty($tracesConfig['username']) || empty($tracesConfig['auth_key'])) {
            throw new Exception('TRACES API configuration not found');
        }

        // Create TracesNtEuImportClient
        $client = new TracesNtEuImportClient(
            $tracesConfig['username'],
            $tracesConfig['auth_key'],
            $tracesConfig['client_id'],
            $tracesConfig['use_production']
        );

        // Build search criteria
        $criteria = [
            'pageSize' => $request->input('page_size', 50),
            'offset' => $request->input('offset', 0),
        ];

        if ($request->has('status')) {
            $criteria['status'] = $request->input('status');
        }

        if ($request->has('local_id')) {
            $criteria['localID'] = $request->input('local_id');
        }

        if ($request->has('cn_code')) {
            $criteria['cnCode'] = $request->input('cn_code');
            $criteria['cnCodeExactMatch'] = $request->input('cn_code_exact_match', false);
        }

        // Add country filters
        $countryFields = [
            'country_of_issuance' => 'countryOfIssuance',
            'country_of_consignor' => 'countryOfConsignor',
            'country_of_consignee' => 'countryOfConsignee',
            'country_of_dispatch' => 'countryOfDispatch'
        ];

        foreach ($countryFields as $requestField => $criteriaField) {
            if ($request->has($requestField)) {
                $criteria[$criteriaField] = $request->input($requestField);
            }
        }

        // Add date range filters
        if ($request->has('create_date_from') || $request->has('create_date_to')) {
            $criteria['createDateTimeRange'] = [];
            if ($request->has('create_date_from')) {
                $criteria['createDateTimeRange']['from'] = $request->input('create_date_from') . 'T00:00:00Z';
            }
            if ($request->has('create_date_to')) {
                $criteria['createDateTimeRange']['to'] = $request->input('create_date_to') . 'T23:59:59Z';
            }
        }

        if ($request->has('update_date_from') || $request->has('update_date_to')) {
            $criteria['updateDateTimeRange'] = [];
            if ($request->has('update_date_from')) {
                $criteria['updateDateTimeRange']['from'] = $request->input('update_date_from') . 'T00:00:00Z';
            }
            if ($request->has('update_date_to')) {
                $criteria['updateDateTimeRange']['to'] = $request->input('update_date_to') . 'T23:59:59Z';
            }
        }

        if ($request->has('certification_date_from') || $request->has('certification_date_to')) {
            $criteria['certificationDateTimeRange'] = [];
            if ($request->has('certification_date_from')) {
                $criteria['certificationDateTimeRange']['from'] = $request->input('certification_date_from') . 'T00:00:00Z';
            }
            if ($request->has('certification_date_to')) {
                $criteria['certificationDateTimeRange']['to'] = $request->input('certification_date_to') . 'T23:59:59Z';
            }
        }

        $results = $client->findEuImportCertificate($criteria);

        return response()->json([
            'success' => true,
            'message' => 'Search completed successfully',
            'source' => 'traces_api',
            'results' => $results
        ]);
    }

    /**
     * Search in local database
     */
    private function searchInDatabase(Request $request): JsonResponse
    {
        $criteria = $request->only([
            'status', 'local_id', 'country_of_issuance', 'country_of_consignor',
            'country_of_consignee', 'country_of_dispatch', 'consignor_name',
            'consignee_name', 'create_date_from', 'create_date_to',
            'update_date_from', 'update_date_to', 'certification_date_from',
            'certification_date_to'
        ]);

        $query = EuImportCertificate::searchByCriteria($criteria);

        // Handle CN code search
        if ($request->has('cn_code')) {
            $cnCodes = $request->input('cn_code');
            $exactMatch = $request->input('cn_code_exact_match', false);

            $query->whereHas('commodities', function ($q) use ($cnCodes, $exactMatch) {
                $q->where('system_id', 'CN');

                if (is_array($cnCodes)) {
                    if ($exactMatch) {
                        $q->whereIn('class_code', $cnCodes);
                    } else {
                        $q->where(function ($subQ) use ($cnCodes) {
                            foreach ($cnCodes as $code) {
                                $subQ->orWhere('class_code', 'like', $code . '%');
                            }
                        });
                    }
                } else {
                    if ($exactMatch) {
                        $q->where('class_code', $cnCodes);
                    } else {
                        $q->where('class_code', 'like', $cnCodes . '%');
                    }
                }
            });
        }

        $pageSize = $request->input('page_size', 50);
        $offset = $request->input('offset', 0);

        $results = $query->with(['commodities'])
            ->skip($offset)
            ->take($pageSize)
            ->get();

        $total = $query->count();

        return response()->json([
            'success' => true,
            'message' => 'Search completed successfully',
            'source' => 'database',
            'results' => [
                'page_size' => $pageSize,
                'offset' => $offset,
                'total' => $total,
                'certificates' => $results
            ]
        ]);
    }

    // Helper methods for data extraction
    private function extractStatusFromData(array $data): ?int
    {
        // Extract status from the certificate data
        // This will depend on the actual structure returned by the API
        return null; // Placeholder
    }

    private function extractStatusNameFromData(array $data): ?string
    {
        // Extract status name from the certificate data
        return null; // Placeholder
    }

    private function extractLocalIdFromData(array $data): ?string
    {
        // Extract local ID (Field I.21a) from the certificate data
        return null; // Placeholder
    }

    private function extractCountryFromParties(array $data, string $type): ?string
    {
        if (!isset($data['parties'])) {
            return null;
        }

        foreach ($data['parties'] as $party) {
            $partyType = strtolower($party['type'] ?? '');
            if (strpos($partyType, $type) !== false) {
                return $party['address']['country_id'] ?? null;
            }
        }

        return null;
    }

    private function extractPartyName(array $data, string $partyType): ?string
    {
        if (!isset($data['parties'])) {
            return null;
        }

        foreach ($data['parties'] as $party) {
            if (($party['type'] ?? '') === $partyType) {
                return $party['name'] ?? null;
            }
        }

        return null;
    }

    private function extractCountriesOfOrigin(array $data): ?array
    {
        // Extract countries of origin from commodity data
        $countries = [];

        if (isset($data['consignments'])) {
            foreach ($data['consignments'] as $consignment) {
                if (isset($consignment['consignment_items'])) {
                    foreach ($consignment['consignment_items'] as $item) {
                        // Extract origin countries from item data
                        // This will depend on the actual API response structure
                    }
                }
            }
        }

        return empty($countries) ? null : array_unique($countries);
    }

    private function extractCountryFromLocations(array $data, string $type): ?string
    {
        if (!isset($data['locations'])) {
            return null;
        }

        foreach ($data['locations'] as $location) {
            $locationType = strtolower($location['type'] ?? '');
            if (strpos($locationType, $type) !== false) {
                // Extract country from location ID or name
                return null; // Placeholder - depends on actual data structure
            }
        }

        return null;
    }

    private function extractCountriesOfTransit(array $data): ?array
    {
        if (!isset($data['locations'])) {
            return null;
        }

        $transitCountries = [];
        foreach ($data['locations'] as $location) {
            if (($location['type'] ?? '') === 'TransitSPSLocation') {
                // Extract country from transit location
                $country = null; // Extract from location data
                if ($country) {
                    $transitCountries[] = $country;
                }
            }
        }

        return empty($transitCountries) ? null : array_unique($transitCountries);
    }

    private function extractCertificationDateTime(array $data): ?string
    {
        if (!isset($data['authentication'])) {
            return null;
        }

        foreach ($data['authentication'] as $auth) {
            if (isset($auth['actual_date_time'])) {
                return $auth['actual_date_time'];
            }
        }

        return null;
    }

    private function checkHasControls(array $data): bool
    {
        // Check if the certificate has laboratory tests or controls
        // This will depend on the actual API response structure
        return false; // Placeholder
    }
}
