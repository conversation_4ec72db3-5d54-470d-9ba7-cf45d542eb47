<?php

namespace App\Providers;

use Exception;

/**
 * TRACES NT EU-Import Certificate Service Client
 * 
 * This client implements the EU-Import certificate retrieval service
 * as documented in TNT-UN-CEFACT-WebService-for-IMPORT-retrieve-V01.txt
 */
class TracesNtEuImportClient
{
    private $username;
    private $authKey;
    private $clientId;
    private $useProduction;

    public function __construct($username, $authKey, $clientId, $useProduction = false)
    {
        $this->username = $username;
        $this->authKey = $authKey;
        $this->clientId = $clientId;
        $this->useProduction = $useProduction;
    }

    /**
     * Get EU-Import certificate XML by reference number
     *
     * @param string $importReference The EU-Import TNT Reference number (e.g., "IMPORT.EU.IT.2020.1000091")
     * @return array The certificate data in SPSCertificate format
     * @throws Exception If certificate not found or permission denied
     */
    public function getEuImportCertificate($importReference)
    {
        $endpoint = $this->useProduction
            ? 'https://webgate.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01'
            : 'https://webgate.acceptance.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01';

        \Log::info('TRACES API Get EU-Import Certificate by Reference', [
            'import_reference' => $importReference,
            'endpoint' => $endpoint,
            'timestamp' => gmdate('Y-m-d H:i:s T')
        ]);

        $soapRequest = $this->createGetEuImportCertificateRequest($importReference);

        try {
            $response = $this->sendSoapRequest($endpoint, $soapRequest, 'getEuImportCertificate');
            
            \Log::info('TRACES API Get EU-Import Certificate Response', [
                'import_reference' => $importReference,
                'response_size' => strlen($response)
            ]);
            
            $parsedData = $this->parseEuImportCertificateResponse($response);
            // Add the raw XML to the parsed data for storage
            $parsedData['raw_xml'] = $response;
            return $parsedData;
        } catch (Exception $e) {
            // If we get an authentication error, try different timestamp methods
            if (strpos($e->getMessage(), 'SOAP Fault') !== false ||
                strpos($e->getMessage(), 'Unauthenticated') !== false) {

                \Log::warning('TRACES API authentication failed for getEuImportCertificate, trying alternative methods', [
                    'error' => $e->getMessage(),
                    'import_reference' => $importReference
                ]);

                return $this->tryAlternativeAuthMethodsForGetEuImportCertificate($importReference, $endpoint);
            }

            throw $e;
        }
    }

    /**
     * Get EU-Import certificate PDF by reference number
     *
     * @param string $importReference The EU-Import TNT Reference number
     * @param array $extraLanguageCodes Additional language codes for multilingual PDF
     * @return string Base64 encoded PDF content
     * @throws Exception If certificate not found or permission denied
     */
    public function getEuImportPdfCertificate($importReference, $extraLanguageCodes = [])
    {
        $endpoint = $this->useProduction
            ? 'https://webgate.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01'
            : 'https://webgate.acceptance.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01';

        \Log::info('TRACES API Get EU-Import PDF Certificate', [
            'import_reference' => $importReference,
            'extra_languages' => $extraLanguageCodes,
            'endpoint' => $endpoint
        ]);

        $soapRequest = $this->createGetEuImportPdfCertificateRequest($importReference, $extraLanguageCodes);

        try {
            $response = $this->sendSoapRequest($endpoint, $soapRequest, 'getEuImportPdfCertificate');
            return $this->parsePdfResponse($response);
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'SOAP Fault') !== false ||
                strpos($e->getMessage(), 'Unauthenticated') !== false) {

                \Log::warning('TRACES API authentication failed for getEuImportPdfCertificate', [
                    'error' => $e->getMessage(),
                    'import_reference' => $importReference
                ]);

                return $this->tryAlternativeAuthMethodsForGetEuImportPdf($importReference, $extraLanguageCodes, $endpoint);
            }

            throw $e;
        }
    }

    /**
     * Get electronically signed PDF of EU-Import certificate
     *
     * @param string $importReference The EU-Import TNT Reference number
     * @return string Base64 encoded signed PDF content
     * @throws Exception If certificate not found, permission denied, or signed PDF not available
     */
    public function getEuImportSignedPdfCertificate($importReference)
    {
        $endpoint = $this->useProduction
            ? 'https://webgate.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01'
            : 'https://webgate.acceptance.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01';

        \Log::info('TRACES API Get EU-Import Signed PDF Certificate', [
            'import_reference' => $importReference,
            'endpoint' => $endpoint
        ]);

        $soapRequest = $this->createGetEuImportSignedPdfCertificateRequest($importReference);

        try {
            $response = $this->sendSoapRequest($endpoint, $soapRequest, 'getEuImportSignedPdfCertificate');
            return $this->parsePdfResponse($response);
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'SOAP Fault') !== false ||
                strpos($e->getMessage(), 'Unauthenticated') !== false) {

                \Log::warning('TRACES API authentication failed for getEuImportSignedPdfCertificate', [
                    'error' => $e->getMessage(),
                    'import_reference' => $importReference
                ]);

                return $this->tryAlternativeAuthMethodsForGetEuImportSignedPdf($importReference, $endpoint);
            }

            throw $e;
        }
    }

    /**
     * Find EU-Import certificates using search criteria
     *
     * @param array $criteria Search criteria as defined in the API documentation
     * @return array Array of EU-Import certificate summaries
     * @throws Exception If search parameters are invalid
     */
    public function findEuImportCertificate($criteria)
    {
        $endpoint = $this->useProduction
            ? 'https://webgate.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01'
            : 'https://webgate.acceptance.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01';

        \Log::info('TRACES API Find EU-Import Certificates', [
            'criteria' => $criteria,
            'endpoint' => $endpoint
        ]);

        $soapRequest = $this->createFindEuImportCertificateRequest($criteria);

        try {
            $response = $this->sendSoapRequest($endpoint, $soapRequest, 'findEuImportCertificate');
            return $this->parseFindEuImportResponse($response);
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'SOAP Fault') !== false ||
                strpos($e->getMessage(), 'Unauthenticated') !== false) {

                \Log::warning('TRACES API authentication failed for findEuImportCertificate', [
                    'error' => $e->getMessage(),
                    'criteria' => $criteria
                ]);

                return $this->tryAlternativeAuthMethodsForFindEuImport($criteria, $endpoint);
            }

            throw $e;
        }
    }

    /**
     * Save XML response to file for analysis
     *
     * @param string $xmlContent The XML content to save
     * @param string $filename The filename to save to
     * @return string The full path to the saved file
     */
    public function saveXmlResponse($xmlContent, $filename = null)
    {
        if (!$filename) {
            $filename = 'eu_import_response_' . date('Y-m-d_H-i-s') . '.xml';
        }

        $storagePath = storage_path('app/traces_responses');

        // Create directory if it doesn't exist
        if (!file_exists($storagePath)) {
            mkdir($storagePath, 0755, true);
        }

        $fullPath = $storagePath . '/' . $filename;
        file_put_contents($fullPath, $xmlContent);

        \Log::info('EU-Import XML response saved', [
            'filename' => $filename,
            'path' => $fullPath,
            'size' => strlen($xmlContent)
        ]);

        return $fullPath;
    }

    /**
     * Create SOAP request for getEuImportCertificate method
     */
    private function createGetEuImportCertificateRequest($importReference)
    {
        $body = '<euimport:GetEuImportCertificateRequest xmlns:euimport="http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01">
            <euimport:ID>' . htmlspecialchars($importReference) . '</euimport:ID>
        </euimport:GetEuImportCertificateRequest>';

        return $this->createSoapRequest($body);
    }

    /**
     * Create SOAP request for getEuImportPdfCertificate method
     */
    private function createGetEuImportPdfCertificateRequest($importReference, $extraLanguageCodes = [])
    {
        $extraLanguagesXml = '';
        foreach ($extraLanguageCodes as $langCode) {
            $extraLanguagesXml .= '<euimport:ExtraLanguageCode>' . htmlspecialchars($langCode) . '</euimport:ExtraLanguageCode>';
        }

        $body = '<euimport:GetEuImportPdfCertificateRequest xmlns:euimport="http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01">
            <euimport:ID>' . htmlspecialchars($importReference) . '</euimport:ID>
            ' . $extraLanguagesXml . '
        </euimport:GetEuImportPdfCertificateRequest>';

        return $this->createSoapRequest($body);
    }

    /**
     * Create SOAP request for getEuImportSignedPdfCertificate method
     */
    private function createGetEuImportSignedPdfCertificateRequest($importReference)
    {
        $body = '<euimport:GetEuImportSignedPdfCertificateRequest xmlns:euimport="http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01">
            <euimport:ID>' . htmlspecialchars($importReference) . '</euimport:ID>
        </euimport:GetEuImportSignedPdfCertificateRequest>';

        return $this->createSoapRequest($body);
    }

    /**
     * Create SOAP request for findEuImportCertificate method
     */
    private function createFindEuImportCertificateRequest($criteria)
    {
        $criteriaXml = '';

        // Required parameters
        if (isset($criteria['pageSize'])) {
            $criteriaXml .= '<euimport:PageSize>' . intval($criteria['pageSize']) . '</euimport:PageSize>';
        } else {
            $criteriaXml .= '<euimport:PageSize>50</euimport:PageSize>'; // Default page size
        }

        if (isset($criteria['offset'])) {
            $criteriaXml .= '<euimport:Offset>' . intval($criteria['offset']) . '</euimport:Offset>';
        } else {
            $criteriaXml .= '<euimport:Offset>0</euimport:Offset>'; // Default offset
        }

        // Optional status filter
        if (isset($criteria['status'])) {
            if (is_array($criteria['status'])) {
                foreach ($criteria['status'] as $status) {
                    $criteriaXml .= '<euimport:Status>' . intval($status) . '</euimport:Status>';
                }
            } else {
                $criteriaXml .= '<euimport:Status>' . intval($criteria['status']) . '</euimport:Status>';
            }
        }

        // Optional local ID filter
        if (isset($criteria['localID'])) {
            $criteriaXml .= '<euimport:LocalID>' . htmlspecialchars($criteria['localID']) . '</euimport:LocalID>';
        }

        // Optional CN Code filter
        if (isset($criteria['cnCode'])) {
            if (is_array($criteria['cnCode'])) {
                foreach ($criteria['cnCode'] as $cnCode) {
                    $criteriaXml .= '<euimport:CNCode>' . htmlspecialchars($cnCode) . '</euimport:CNCode>';
                }
            } else {
                $criteriaXml .= '<euimport:CNCode>' . htmlspecialchars($criteria['cnCode']) . '</euimport:CNCode>';
            }
        }

        // Optional CN Code exact match
        if (isset($criteria['cnCodeExactMatch'])) {
            $criteriaXml .= '<euimport:CNCode.exactMatch>' . ($criteria['cnCodeExactMatch'] ? 'true' : 'false') . '</euimport:CNCode.exactMatch>';
        }

        // Date range filters
        $dateRangeFields = [
            'createDateTimeRange' => 'CreateDateTimeRange',
            'updateDateTimeRange' => 'UpdateDateTimeRange',
            'statusChangeDateTimeRange' => 'StatusChangeDateTimeRange',
            'declarationDateTimeRange' => 'DeclarationDateTimeRange',
            'certificationDateTimeRange' => 'CertificationDateTimeRange'
        ];

        foreach ($dateRangeFields as $criteriaKey => $xmlElement) {
            if (isset($criteria[$criteriaKey])) {
                $range = $criteria[$criteriaKey];
                $criteriaXml .= '<euimport:' . $xmlElement . '>';
                if (isset($range['from'])) {
                    $criteriaXml .= '<base:From>' . htmlspecialchars($range['from']) . '</base:From>';
                }
                if (isset($range['to'])) {
                    $criteriaXml .= '<base:To>' . htmlspecialchars($range['to']) . '</base:To>';
                }
                $criteriaXml .= '</euimport:' . $xmlElement . '>';
            }
        }

        // Country filters
        $countryFields = [
            'countryOfIssuance' => 'CountryOfIssuance',
            'countryOfConsignor' => 'CountryOfConsignor',
            'countryOfConsignee' => 'CountryOfConsignee',
            'countryOfOrigin' => 'CountryOfOrigin',
            'countryOfDestination' => 'CountryOfDestination',
            'countryOfDispatch' => 'CountryOfDispatch',
            'countryOfPlaceOfLoading' => 'CountryOfPlaceOfLoading',
            'countryOfTransit' => 'CountryOfTransit'
        ];

        foreach ($countryFields as $criteriaKey => $xmlElement) {
            if (isset($criteria[$criteriaKey])) {
                $criteriaXml .= '<euimport:' . $xmlElement . '>' . htmlspecialchars($criteria[$criteriaKey]) . '</euimport:' . $xmlElement . '>';
            }
        }

        // Authority codes
        if (isset($criteria['centralCompetentAuthorityActivityCode'])) {
            $criteriaXml .= '<euimport:CentralCompetentAuthorityActivityCode>' . htmlspecialchars($criteria['centralCompetentAuthorityActivityCode']) . '</euimport:CentralCompetentAuthorityActivityCode>';
        }

        if (isset($criteria['localCompetentAuthorityActivityCode'])) {
            $criteriaXml .= '<euimport:LocalCompetentAuthorityActivityCode>' . htmlspecialchars($criteria['localCompetentAuthorityActivityCode']) . '</euimport:LocalCompetentAuthorityActivityCode>';
        }

        $body = '<euimport:FindEuImportCertificateRequest xmlns:euimport="http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01" xmlns:base="http://ec.europa.eu/sanco/tracesnt/base/v4">
            ' . $criteriaXml . '
        </euimport:FindEuImportCertificateRequest>';

        return $this->createSoapRequest($body);
    }

    /**
     * Create base SOAP request with authentication headers
     */
    private function createSoapRequest($body)
    {
        date_default_timezone_set('UTC');

        $nonceRaw = random_bytes(16);
        $nonce = base64_encode($nonceRaw);

        // Get current time and apply timestamp adjustments for TRACES API synchronization
        $now = new \DateTime('now', new \DateTimeZone('UTC'));

        // TRACES API timestamp synchronization fixes:
        // 1. Subtract a few seconds to account for network delay and server time drift
        $now->modify('-10 seconds');

        // 2. Use longer expiration time (15 minutes instead of 5) as suggested in docs
        $created = $now->format('Y-m-d\TH:i:s\Z');
        $expires = (clone $now)->modify('+15 minutes')->format('Y-m-d\TH:i:s\Z');

        // Create password digest: Base64(SHA1(nonce + created + password))
        $passwordDigest = base64_encode(sha1($nonceRaw . $created . $this->authKey, true));

        return '<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope
    xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
    xmlns:v3="http://ec.europa.eu/sanco/tracesnt/base/v3"
    xmlns:euimport="http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01"
    xmlns:base="http://ec.europa.eu/sanco/tracesnt/base/v4">
    <soapenv:Header>
        <wsse:Security
            xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"
            xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
            soapenv:mustUnderstand="1">
            <wsse:UsernameToken wsu:Id="UsernameToken-' . uniqid() . '">
                <wsse:Username>' . htmlspecialchars($this->username) . '</wsse:Username>
                <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest">' . $passwordDigest . '</wsse:Password>
                <wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">' . $nonce . '</wsse:Nonce>
                <wsu:Created>' . $created . '</wsu:Created>
            </wsse:UsernameToken>
            <wsu:Timestamp wsu:Id="TS-' . uniqid() . '">
                <wsu:Created>' . $created . '</wsu:Created>
                <wsu:Expires>' . $expires . '</wsu:Expires>
            </wsu:Timestamp>
        </wsse:Security>
        <v3:LanguageCode>en</v3:LanguageCode>
        <v3:WebServiceClientId>' . htmlspecialchars($this->clientId) . '</v3:WebServiceClientId>
    </soapenv:Header>
    <soapenv:Body>
        ' . $body . '
    </soapenv:Body>
</soapenv:Envelope>';
    }

    /**
     * Send SOAP request to TRACES API
     */
    private function sendSoapRequest($endpoint, $soapRequest, $soapAction)
    {
        $headers = [
            'Content-Type: text/xml; charset=utf-8',
            'SOAPAction: "' . $soapAction . '"',
            'Content-Length: ' . strlen($soapRequest),
            'User-Agent: ONISPA-Laravel-EuImport-Client/1.0'
        ];

        // Get configuration from Laravel config
        $tracesConfig = config('services.traces', []);
        $timeout = $tracesConfig['timeout'] ?? 60;
        $verifySSL = $tracesConfig['verify_ssl'] ?? true;

        // Log request for debugging
        \Log::info('TRACES EU-Import API Request', [
            'endpoint' => $endpoint,
            'soap_action' => $soapAction,
            'request_size' => strlen($soapRequest),
            'timeout' => $timeout,
            'verify_ssl' => $verifySSL
        ]);

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $endpoint,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $soapRequest,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => $timeout,
            CURLOPT_CONNECTTIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => $verifySSL,
            CURLOPT_SSL_VERIFYHOST => $verifySSL ? 2 : 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3,
            CURLOPT_USERAGENT => 'ONISPA-Laravel-EuImport-Client/1.0',
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        if ($response === false) {
            throw new Exception("CURL Error: $curlError");
        }

        if ($httpCode !== 200) {
            \Log::error('TRACES EU-Import API HTTP Error', [
                'http_code' => $httpCode,
                'response_preview' => substr($response, 0, 1000)
            ]);
            throw new Exception("HTTP Error $httpCode: " . substr($response, 0, 500));
        }

        // Check for authentication errors in the response
        if (strpos($response, 'Unauthenticated\Exception') !== false) {
            \Log::error('TRACES EU-Import API Authentication Error', [
                'response_preview' => substr($response, 0, 1000)
            ]);
            throw new Exception('Authentication failed: Invalid credentials or expired session');
        }

        return $response;
    }

    /**
     * Parse EU-Import certificate XML response
     */
    private function parseEuImportCertificateResponse($xmlResponse)
    {
        // Save the raw response for analysis
        $this->saveXmlResponse($xmlResponse, 'eu_import_certificate_' . date('Y-m-d_H-i-s') . '.xml');

        $dom = new \DOMDocument();
        libxml_use_internal_errors(true);

        if (!$dom->loadXML($xmlResponse)) {
            $errors = libxml_get_errors();
            $errorMessages = array_map(fn($e) => $e->message, $errors);
            throw new Exception("Failed to parse XML response:\n" . implode("\n", $errorMessages));
        }

        $xpath = new \DOMXPath($dom);
        $xpath->registerNamespace('soap', 'http://schemas.xmlsoap.org/soap/envelope/');
        $xpath->registerNamespace('S', 'http://schemas.xmlsoap.org/soap/envelope/');
        $xpath->registerNamespace('ns3', 'urn:un:unece:uncefact:data:standard:ReusableAggregateBusinessInformationEntity:21');
        $xpath->registerNamespace('ns4', 'urn:un:unece:uncefact:data:standard:UnqualifiedDataType:21');
        $xpath->registerNamespace('ns5', 'urn:un:unece:uncefact:data:standard:SPSCertificate:17');
        $xpath->registerNamespace('ns6', 'http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01');

        // Check for SOAP faults
        $faultNodes = $xpath->query('//S:Fault | //soap:Fault');
        if ($faultNodes->length > 0) {
            $faultCode = $xpath->query('.//faultcode', $faultNodes->item(0))->item(0)?->nodeValue ?? 'Unknown';
            $faultString = $xpath->query('.//faultstring', $faultNodes->item(0))->item(0)?->nodeValue ?? 'Unknown error';
            throw new Exception("SOAP Fault: $faultCode - $faultString");
        }

        // Find the SPSCertificate element - it's nested in the response
        $certificateNodes = $xpath->query('//ns6:SPSCertificate');
        if ($certificateNodes->length === 0) {
            throw new Exception('No SPSCertificate found in response');
        }

        $certificate = $certificateNodes->item(0);

        // Parse the certificate data according to UN/CEFACT structure
        // The SPSExchangedDocument contains the main certificate info
        $exchangedDoc = $xpath->query('.//ns5:SPSExchangedDocument', $certificate)->item(0);

        $certificateData = [
            'id' => $xpath->query('.//ns3:ID', $exchangedDoc)->item(0)?->nodeValue,
            'name' => $xpath->query('.//ns3:Name', $exchangedDoc)->item(0)?->nodeValue,
            'issue_date_time' => $xpath->query('.//ns3:IssueDateTime/ns4:DateTime', $exchangedDoc)->item(0)?->nodeValue,
            'type_code' => $xpath->query('.//ns3:TypeCode', $exchangedDoc)->item(0)?->nodeValue,
            'type_name' => $xpath->query('.//ns3:TypeCode/@name', $exchangedDoc)->item(0)?->nodeValue,
            'status_code' => $xpath->query('.//ns3:StatusCode', $exchangedDoc)->item(0)?->nodeValue,
            'status_name' => $xpath->query('.//ns3:StatusCode/@name', $exchangedDoc)->item(0)?->nodeValue,
            'consignments' => [],
            'parties' => [],
            'locations' => [],
            'referenced_documents' => [],
            'authentication' => [],
            'notes' => []
        ];

        // Parse notes
        $noteNodes = $xpath->query('.//ns3:IncludedSPSNote', $exchangedDoc);
        foreach ($noteNodes as $note) {
            $certificateData['notes'][] = [
                'content' => $xpath->query('.//ns3:Content', $note)->item(0)?->nodeValue,
                'subject_code' => $xpath->query('.//ns3:SubjectCode', $note)->item(0)?->nodeValue,
                'subject_name' => $xpath->query('.//ns3:SubjectCode/@name', $note)->item(0)?->nodeValue,
                'content_code' => $xpath->query('.//ns3:ContentCode', $note)->item(0)?->nodeValue,
                'content_name' => $xpath->query('.//ns3:ContentCode/@name', $note)->item(0)?->nodeValue
            ];
        }

        // Parse referenced documents
        $refDocNodes = $xpath->query('.//ns3:ReferenceSPSReferencedDocument', $exchangedDoc);
        foreach ($refDocNodes as $refDoc) {
            $certificateData['referenced_documents'][] = [
                'id' => $xpath->query('.//ns3:ID', $refDoc)->item(0)?->nodeValue,
                'type_code' => $xpath->query('.//ns3:TypeCode', $refDoc)->item(0)?->nodeValue,
                'type_name' => $xpath->query('.//ns3:TypeCode/@name', $refDoc)->item(0)?->nodeValue,
                'relationship_type_code' => $xpath->query('.//ns3:RelationshipTypeCode', $refDoc)->item(0)?->nodeValue,
                'relationship_type_name' => $xpath->query('.//ns3:RelationshipTypeCode/@name', $refDoc)->item(0)?->nodeValue,
                'information' => $xpath->query('.//ns3:Information', $refDoc)->item(0)?->nodeValue,
                'attachment_uri' => $xpath->query('.//ns3:AttachmentBinaryObject/@uri', $refDoc)->item(0)?->nodeValue,
                'attachment_format' => $xpath->query('.//ns3:AttachmentBinaryObject/@format', $refDoc)->item(0)?->nodeValue,
                'attachment_mime_code' => $xpath->query('.//ns3:AttachmentBinaryObject/@mimeCode', $refDoc)->item(0)?->nodeValue
            ];
        }

        // Parse consignments
        $consignmentNodes = $xpath->query('.//ns5:SPSConsignment', $certificate);
        foreach ($consignmentNodes as $consignment) {
            $consignmentData = [
                'export_exit_date_time' => $xpath->query('.//ns3:ExportExitDateTime/ns4:DateTime', $consignment)->item(0)?->nodeValue,
                'transport_movement' => [],
                'consignment_items' => [],
                'parties' => []
            ];

            // Parse transport movement
            $transportNodes = $xpath->query('.//ns3:MainCarriageSPSTransportMovement', $consignment);
            foreach ($transportNodes as $transport) {
                $consignmentData['transport_movement'][] = [
                    'id' => $xpath->query('.//ns3:ID', $transport)->item(0)?->nodeValue,
                    'mode_code' => $xpath->query('.//ns3:ModeCode', $transport)->item(0)?->nodeValue,
                    'mode_name' => $xpath->query('.//ns3:ModeCode/@name', $transport)->item(0)?->nodeValue
                ];
            }

            // Parse trade line items (products)
            $tradeLineNodes = $xpath->query('.//ns3:IncludedSPSTradeLineItem', $item);
            foreach ($tradeLineNodes as $tradeLine) {
                $tradeLineData = [
                    'sequence_numeric' => $xpath->query('.//ns3:SequenceNumeric', $tradeLine)->item(0)?->nodeValue,
                    'description' => $xpath->query('.//ns3:Description', $tradeLine)->item(0)?->nodeValue,
                    'scientific_name' => $xpath->query('.//ns3:ScientificName', $tradeLine)->item(0)?->nodeValue,
                    'production_batch_id' => $xpath->query('.//ns3:ProductionBatchID', $tradeLine)->item(0)?->nodeValue,
                    'net_weight' => $xpath->query('.//ns3:NetWeightMeasure', $tradeLine)->item(0)?->nodeValue,
                    'net_weight_unit' => $xpath->query('.//ns3:NetWeightMeasure/@unitCode', $tradeLine)->item(0)?->nodeValue,
                    'gross_weight' => $xpath->query('.//ns3:GrossWeightMeasure', $tradeLine)->item(0)?->nodeValue,
                    'gross_weight_unit' => $xpath->query('.//ns3:GrossWeightMeasure/@unitCode', $tradeLine)->item(0)?->nodeValue,
                    'classifications' => [],
                    'packages' => [],
                    'processes' => []
                ];

                // Parse classifications (CN codes, FAO codes, etc.)
                $classificationNodes = $xpath->query('.//ns3:ApplicableSPSClassification', $tradeLine);
                foreach ($classificationNodes as $classification) {
                    $tradeLineData['classifications'][] = [
                        'system_id' => $xpath->query('.//ns3:SystemID', $classification)->item(0)?->nodeValue,
                        'system_name' => $xpath->query('.//ns3:SystemName', $classification)->item(0)?->nodeValue,
                        'class_code' => $xpath->query('.//ns3:ClassCode', $classification)->item(0)?->nodeValue,
                        'class_name' => $xpath->query('.//ns3:ClassName', $classification)->item(0)?->nodeValue
                    ];
                }

                // Parse packages
                $packageNodes = $xpath->query('.//ns3:PhysicalSPSPackage', $tradeLine);
                foreach ($packageNodes as $package) {
                    $tradeLineData['packages'][] = [
                        'level_code' => $xpath->query('.//ns3:LevelCode', $package)->item(0)?->nodeValue,
                        'level_name' => $xpath->query('.//ns3:LevelCode/@name', $package)->item(0)?->nodeValue,
                        'type_code' => $xpath->query('.//ns3:TypeCode', $package)->item(0)?->nodeValue,
                        'type_name' => $xpath->query('.//ns3:TypeCode/@name', $package)->item(0)?->nodeValue,
                        'item_quantity' => $xpath->query('.//ns3:ItemQuantity', $package)->item(0)?->nodeValue
                    ];
                }

                // Parse processes
                $processNodes = $xpath->query('.//ns3:AppliedSPSProcess', $tradeLine);
                foreach ($processNodes as $process) {
                    $processData = [
                        'type_code' => $xpath->query('.//ns3:TypeCode', $process)->item(0)?->nodeValue,
                        'type_name' => $xpath->query('.//ns3:TypeCode/@name', $process)->item(0)?->nodeValue,
                        'completion_start_date' => $xpath->query('.//ns3:CompletionSPSPeriod/ns3:StartDateTime/ns4:DateTime', $process)->item(0)?->nodeValue,
                        'operator' => []
                    ];

                    // Parse operator party
                    $operatorNode = $xpath->query('.//ns3:OperatorSPSParty', $process)->item(0);
                    if ($operatorNode) {
                        $processData['operator'] = [
                            'id' => $xpath->query('.//ns3:ID', $operatorNode)->item(0)?->nodeValue,
                            'name' => $xpath->query('.//ns3:Name', $operatorNode)->item(0)?->nodeValue,
                            'type_code' => $xpath->query('.//ns3:TypeCode', $operatorNode)->item(0)?->nodeValue,
                            'type_name' => $xpath->query('.//ns3:TypeCode/@name', $operatorNode)->item(0)?->nodeValue,
                            'address' => [
                                'line_one' => $xpath->query('.//ns3:SpecifiedSPSAddress/ns3:LineOne', $operatorNode)->item(0)?->nodeValue,
                                'city_name' => $xpath->query('.//ns3:SpecifiedSPSAddress/ns3:CityName', $operatorNode)->item(0)?->nodeValue,
                                'country_id' => $xpath->query('.//ns3:SpecifiedSPSAddress/ns3:CountryID', $operatorNode)->item(0)?->nodeValue,
                                'country_name' => $xpath->query('.//ns3:SpecifiedSPSAddress/ns3:CountryName', $operatorNode)->item(0)?->nodeValue,
                                'country_subdivision' => $xpath->query('.//ns3:SpecifiedSPSAddress/ns3:CountrySubDivisionName', $operatorNode)->item(0)?->nodeValue
                            ]
                        ];
                    }

                    $tradeLineData['processes'][] = $processData;
                }

                $itemData['trade_lines'][] = $tradeLineData;
            }

            // Parse consignment items
            $itemNodes = $xpath->query('.//ns3:IncludedSPSConsignmentItem', $consignment);
            foreach ($itemNodes as $item) {
                $itemData = [
                    'nature_identification' => [],
                    'trade_lines' => []
                ];

                // Parse nature identification
                $natureNodes = $xpath->query('.//ns3:NatureIdentificationSPSCargo', $item);
                foreach ($natureNodes as $nature) {
                    $itemData['nature_identification'][] = [
                        'type_code' => $xpath->query('.//ns3:TypeCode', $nature)->item(0)?->nodeValue,
                        'type_name' => $xpath->query('.//ns3:TypeCode/@name', $nature)->item(0)?->nodeValue
                    ];
                }

                $consignmentData['consignment_items'][] = $itemData;
            }

            // Parse parties (consignor, consignee, etc.)
            $partyNodes = $xpath->query('.//ns3:ConsignorSPSParty | .//ns3:ConsigneeSPSParty | .//ns3:DespatchSPSParty | .//ns3:DeliverySPSParty | .//ns3:CustomsTransitAgentSPSParty', $consignment);
            foreach ($partyNodes as $party) {
                $partyData = [
                    'id' => $xpath->query('.//ns3:ID', $party)->item(0)?->nodeValue,
                    'name' => $xpath->query('.//ns3:Name', $party)->item(0)?->nodeValue,
                    'role_code' => $xpath->query('.//ns3:RoleCode', $party)->item(0)?->nodeValue,
                    'role_name' => $xpath->query('.//ns3:RoleCode/@name', $party)->item(0)?->nodeValue,
                    'type_code' => $xpath->query('.//ns3:TypeCode', $party)->item(0)?->nodeValue,
                    'type_name' => $xpath->query('.//ns3:TypeCode/@name', $party)->item(0)?->nodeValue,
                    'address' => [
                        'postcode' => $xpath->query('.//ns3:SpecifiedSPSAddress/ns3:PostcodeCode', $party)->item(0)?->nodeValue,
                        'line_one' => $xpath->query('.//ns3:SpecifiedSPSAddress/ns3:LineOne', $party)->item(0)?->nodeValue,
                        'city_name' => $xpath->query('.//ns3:SpecifiedSPSAddress/ns3:CityName', $party)->item(0)?->nodeValue,
                        'country_id' => $xpath->query('.//ns3:SpecifiedSPSAddress/ns3:CountryID', $party)->item(0)?->nodeValue,
                        'country_name' => $xpath->query('.//ns3:SpecifiedSPSAddress/ns3:CountryName', $party)->item(0)?->nodeValue,
                        'country_subdivision' => $xpath->query('.//ns3:SpecifiedSPSAddress/ns3:CountrySubDivisionName', $party)->item(0)?->nodeValue
                    ]
                ];

                $consignmentData['parties'][] = $partyData;
                $certificateData['parties'][] = $partyData; // Also add to main certificate
            }

            $certificateData['consignments'][] = $consignmentData;
        }

        // Parse authentication/signatures
        $authNodes = $xpath->query('.//ns3:SignatorySPSAuthentication', $exchangedDoc);
        foreach ($authNodes as $auth) {
            $authData = [
                'type_code' => $xpath->query('.//ns3:TypeCode', $auth)->item(0)?->nodeValue,
                'type_name' => $xpath->query('.//ns3:TypeCode/@name', $auth)->item(0)?->nodeValue,
                'actual_date_time' => $xpath->query('.//ns3:ActualDateTime/ns4:DateTime', $auth)->item(0)?->nodeValue,
                'provider_party' => [
                    'id' => $xpath->query('.//ns3:ProviderSPSParty/ns3:ID', $auth)->item(0)?->nodeValue,
                    'name' => $xpath->query('.//ns3:ProviderSPSParty/ns3:Name', $auth)->item(0)?->nodeValue,
                    'role_code' => $xpath->query('.//ns3:ProviderSPSParty/ns3:RoleCode', $auth)->item(0)?->nodeValue,
                    'role_name' => $xpath->query('.//ns3:ProviderSPSParty/ns3:RoleCode/@name', $auth)->item(0)?->nodeValue,
                    'person_name' => $xpath->query('.//ns3:ProviderSPSParty/ns3:SpecifiedSPSPerson/ns3:Name', $auth)->item(0)?->nodeValue
                ],
                'clauses' => []
            ];

            // Parse clauses
            $clauseNodes = $xpath->query('.//ns3:IncludedSPSClause', $auth);
            foreach ($clauseNodes as $clause) {
                $authData['clauses'][] = [
                    'id' => $xpath->query('.//ns3:ID', $clause)->item(0)?->nodeValue,
                    'content' => $xpath->query('.//ns3:Content', $clause)->item(0)?->nodeValue
                ];
            }

            $certificateData['authentication'][] = $authData;
        }

        return $certificateData;
    }

    /**
     * Parse PDF response (Base64 encoded PDF content)
     */
    private function parsePdfResponse($xmlResponse)
    {
        $dom = new \DOMDocument();
        libxml_use_internal_errors(true);

        if (!$dom->loadXML($xmlResponse)) {
            $errors = libxml_get_errors();
            $errorMessages = array_map(fn($e) => $e->message, $errors);
            throw new Exception("Failed to parse PDF response XML:\n" . implode("\n", $errorMessages));
        }

        $xpath = new \DOMXPath($dom);
        $xpath->registerNamespace('soap', 'http://schemas.xmlsoap.org/soap/envelope/');

        // Check for SOAP faults
        $faultNodes = $xpath->query('//soap:Fault');
        if ($faultNodes->length > 0) {
            $faultCode = $xpath->query('.//faultcode', $faultNodes->item(0))->item(0)?->nodeValue ?? 'Unknown';
            $faultString = $xpath->query('.//faultstring', $faultNodes->item(0))->item(0)?->nodeValue ?? 'Unknown error';
            throw new Exception("SOAP Fault: $faultCode - $faultString");
        }

        // Find the PDF content (usually in a return element)
        $pdfNodes = $xpath->query('//return');
        if ($pdfNodes->length === 0) {
            throw new Exception('No PDF content found in response');
        }

        $pdfContent = $pdfNodes->item(0)->nodeValue;

        // Validate that it's base64 encoded
        if (!base64_decode($pdfContent, true)) {
            throw new Exception('Invalid base64 PDF content received');
        }

        return $pdfContent;
    }

    /**
     * Parse find EU-Import response
     */
    private function parseFindEuImportResponse($xmlResponse)
    {
        // Save the raw response for analysis
        $this->saveXmlResponse($xmlResponse, 'eu_import_find_' . date('Y-m-d_H-i-s') . '.xml');

        $dom = new \DOMDocument();
        libxml_use_internal_errors(true);

        if (!$dom->loadXML($xmlResponse)) {
            $errors = libxml_get_errors();
            $errorMessages = array_map(fn($e) => $e->message, $errors);
            throw new Exception("Failed to parse find response XML:\n" . implode("\n", $errorMessages));
        }

        $xpath = new \DOMXPath($dom);
        $xpath->registerNamespace('soap', 'http://schemas.xmlsoap.org/soap/envelope/');

        // Check for SOAP faults
        $faultNodes = $xpath->query('//soap:Fault');
        if ($faultNodes->length > 0) {
            $faultCode = $xpath->query('.//faultcode', $faultNodes->item(0))->item(0)?->nodeValue ?? 'Unknown';
            $faultString = $xpath->query('.//faultstring', $faultNodes->item(0))->item(0)?->nodeValue ?? 'Unknown error';
            throw new Exception("SOAP Fault: $faultCode - $faultString");
        }

        // Parse the response - this will depend on the actual response structure
        // For now, return the basic structure
        $results = [
            'page_size' => 0,
            'offset' => 0,
            'certificates' => []
        ];

        // Find result elements (structure may vary based on actual API response)
        $resultNodes = $xpath->query('//return');
        foreach ($resultNodes as $result) {
            // Parse individual certificate summary data
            // This will need to be adjusted based on actual response structure
            $results['certificates'][] = [
                'id' => $xpath->query('.//ID', $result)->item(0)?->nodeValue,
                'status' => $xpath->query('.//Status', $result)->item(0)?->nodeValue,
                'local_id' => $xpath->query('.//LocalID', $result)->item(0)?->nodeValue,
                'create_date_time' => $xpath->query('.//CreateDateTime', $result)->item(0)?->nodeValue,
                'update_date_time' => $xpath->query('.//UpdateDateTime', $result)->item(0)?->nodeValue,
                'country_of_issuance' => $xpath->query('.//CountryOfIssuance', $result)->item(0)?->nodeValue,
                'country_of_consignor' => $xpath->query('.//CountryOfConsignor', $result)->item(0)?->nodeValue,
                'country_of_consignee' => $xpath->query('.//CountryOfConsignee', $result)->item(0)?->nodeValue,
                'consignor_name' => $xpath->query('.//ConsignorName', $result)->item(0)?->nodeValue,
                'consignee_name' => $xpath->query('.//ConsigneeName', $result)->item(0)?->nodeValue
            ];
        }

        return $results;
    }

    /**
     * Try alternative authentication methods for getEuImportCertificate
     */
    private function tryAlternativeAuthMethodsForGetEuImportCertificate($importReference, $endpoint)
    {
        $authMethods = [
            'method1' => 'Standard timestamp with 5 second offset',
            'method2' => 'No timestamp offset',
            'method3' => 'Extended expiration time (30 minutes)',
            'method4' => 'Different nonce generation method'
        ];

        foreach ($authMethods as $method => $description) {
            \Log::info("Trying EU-Import authentication method: {$method} - {$description}");

            try {
                $soapRequest = $this->createGetEuImportCertificateRequestWithMethod($importReference, $method);
                $response = $this->sendSoapRequest($endpoint, $soapRequest, 'getEuImportCertificate');

                if (strpos($response, 'SOAP Fault') === false &&
                    strpos($response, 'Unauthenticated') === false) {

                    \Log::info("TRACES EU-Import authentication successful with: {$description}");
                    $parsedData = $this->parseEuImportCertificateResponse($response);
                    // Add the raw XML to the parsed data for storage
                    $parsedData['raw_xml'] = $response;
                    return $parsedData;
                }

            } catch (Exception $e) {
                \Log::warning("Method {$method} failed: " . $e->getMessage());
                continue;
            }
        }

        throw new Exception('All authentication methods failed for getEuImportCertificate');
    }

    /**
     * Try alternative authentication methods for PDF requests
     */
    private function tryAlternativeAuthMethodsForGetEuImportPdf($importReference, $extraLanguageCodes, $endpoint)
    {
        $authMethods = [
            'method1' => 'Standard timestamp with 5 second offset',
            'method2' => 'No timestamp offset',
            'method3' => 'Extended expiration time (30 minutes)',
            'method4' => 'Different nonce generation method'
        ];

        foreach ($authMethods as $method => $description) {
            \Log::info("Trying EU-Import PDF authentication method: {$method} - {$description}");

            try {
                $soapRequest = $this->createGetEuImportPdfCertificateRequestWithMethod($importReference, $extraLanguageCodes, $method);
                $response = $this->sendSoapRequest($endpoint, $soapRequest, 'getEuImportPdfCertificate');

                if (strpos($response, 'SOAP Fault') === false &&
                    strpos($response, 'Unauthenticated') === false) {

                    \Log::info("TRACES EU-Import PDF authentication successful with: {$description}");
                    return $this->parsePdfResponse($response);
                }

            } catch (Exception $e) {
                \Log::warning("PDF Method {$method} failed: " . $e->getMessage());
                continue;
            }
        }

        throw new Exception('All authentication methods failed for getEuImportPdfCertificate');
    }

    /**
     * Try alternative authentication methods for signed PDF requests
     */
    private function tryAlternativeAuthMethodsForGetEuImportSignedPdf($importReference, $endpoint)
    {
        $authMethods = [
            'method1' => 'Standard timestamp with 5 second offset',
            'method2' => 'No timestamp offset',
            'method3' => 'Extended expiration time (30 minutes)',
            'method4' => 'Different nonce generation method'
        ];

        foreach ($authMethods as $method => $description) {
            \Log::info("Trying EU-Import Signed PDF authentication method: {$method} - {$description}");

            try {
                $soapRequest = $this->createGetEuImportSignedPdfCertificateRequestWithMethod($importReference, $method);
                $response = $this->sendSoapRequest($endpoint, $soapRequest, 'getEuImportSignedPdfCertificate');

                if (strpos($response, 'SOAP Fault') === false &&
                    strpos($response, 'Unauthenticated') === false) {

                    \Log::info("TRACES EU-Import Signed PDF authentication successful with: {$description}");
                    return $this->parsePdfResponse($response);
                }

            } catch (Exception $e) {
                \Log::warning("Signed PDF Method {$method} failed: " . $e->getMessage());
                continue;
            }
        }

        throw new Exception('All authentication methods failed for getEuImportSignedPdfCertificate');
    }

    /**
     * Try alternative authentication methods for find requests
     */
    private function tryAlternativeAuthMethodsForFindEuImport($criteria, $endpoint)
    {
        $authMethods = [
            'method1' => 'Standard timestamp with 5 second offset',
            'method2' => 'No timestamp offset',
            'method3' => 'Extended expiration time (30 minutes)',
            'method4' => 'Different nonce generation method'
        ];

        foreach ($authMethods as $method => $description) {
            \Log::info("Trying EU-Import Find authentication method: {$method} - {$description}");

            try {
                $soapRequest = $this->createFindEuImportCertificateRequestWithMethod($criteria, $method);
                $response = $this->sendSoapRequest($endpoint, $soapRequest, 'findEuImportCertificate');

                if (strpos($response, 'SOAP Fault') === false &&
                    strpos($response, 'Unauthenticated') === false) {

                    \Log::info("TRACES EU-Import Find authentication successful with: {$description}");
                    return $this->parseFindEuImportResponse($response);
                }

            } catch (Exception $e) {
                \Log::warning("Find Method {$method} failed: " . $e->getMessage());
                continue;
            }
        }

        throw new Exception('All authentication methods failed for findEuImportCertificate');
    }

    /**
     * Create SOAP request with alternative authentication method
     */
    private function createGetEuImportCertificateRequestWithMethod($importReference, $method)
    {
        $body = '<euimport:GetEuImportCertificateRequest xmlns:euimport="http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01">
            <euimport:ID>' . htmlspecialchars($importReference) . '</euimport:ID>
        </euimport:GetEuImportCertificateRequest>';

        return $this->createSoapRequestWithMethod($body, $method);
    }

    /**
     * Create PDF SOAP request with alternative authentication method
     */
    private function createGetEuImportPdfCertificateRequestWithMethod($importReference, $extraLanguageCodes, $method)
    {
        $extraLanguagesXml = '';
        foreach ($extraLanguageCodes as $langCode) {
            $extraLanguagesXml .= '<euimport:ExtraLanguageCode>' . htmlspecialchars($langCode) . '</euimport:ExtraLanguageCode>';
        }

        $body = '<euimport:GetEuImportPdfCertificateRequest xmlns:euimport="http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01">
            <euimport:ID>' . htmlspecialchars($importReference) . '</euimport:ID>
            ' . $extraLanguagesXml . '
        </euimport:GetEuImportPdfCertificateRequest>';

        return $this->createSoapRequestWithMethod($body, $method);
    }

    /**
     * Create Signed PDF SOAP request with alternative authentication method
     */
    private function createGetEuImportSignedPdfCertificateRequestWithMethod($importReference, $method)
    {
        $body = '<euimport:GetEuImportSignedPdfCertificateRequest xmlns:euimport="http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01">
            <euimport:ID>' . htmlspecialchars($importReference) . '</euimport:ID>
        </euimport:GetEuImportSignedPdfCertificateRequest>';

        return $this->createSoapRequestWithMethod($body, $method);
    }

    /**
     * Create Find SOAP request with alternative authentication method
     */
    private function createFindEuImportCertificateRequestWithMethod($criteria, $method)
    {
        // Reuse the same criteria building logic from the main method
        $criteriaXml = '';

        // Required parameters
        if (isset($criteria['pageSize'])) {
            $criteriaXml .= '<euimport:PageSize>' . intval($criteria['pageSize']) . '</euimport:PageSize>';
        } else {
            $criteriaXml .= '<euimport:PageSize>50</euimport:PageSize>';
        }

        if (isset($criteria['offset'])) {
            $criteriaXml .= '<euimport:Offset>' . intval($criteria['offset']) . '</euimport:Offset>';
        } else {
            $criteriaXml .= '<euimport:Offset>0</euimport:Offset>';
        }

        // Add other criteria as needed (simplified for brevity)
        if (isset($criteria['status'])) {
            if (is_array($criteria['status'])) {
                foreach ($criteria['status'] as $status) {
                    $criteriaXml .= '<euimport:Status>' . intval($status) . '</euimport:Status>';
                }
            } else {
                $criteriaXml .= '<euimport:Status>' . intval($criteria['status']) . '</euimport:Status>';
            }
        }

        $body = '<euimport:FindEuImportCertificateRequest xmlns:euimport="http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01" xmlns:base="http://ec.europa.eu/sanco/tracesnt/base/v4">
            ' . $criteriaXml . '
        </euimport:FindEuImportCertificateRequest>';

        return $this->createSoapRequestWithMethod($body, $method);
    }

    /**
     * Create SOAP request with specific authentication method
     */
    private function createSoapRequestWithMethod($body, $method)
    {
        date_default_timezone_set('UTC');

        $nonceRaw = random_bytes(16);
        $nonce = base64_encode($nonceRaw);

        $now = new \DateTime('now', new \DateTimeZone('UTC'));

        // Apply different timestamp methods
        switch ($method) {
            case 'method1':
                $now->modify('-5 seconds');
                $expirationMinutes = 15;
                break;
            case 'method2':
                // No offset
                $expirationMinutes = 15;
                break;
            case 'method3':
                $now->modify('-10 seconds');
                $expirationMinutes = 30;
                break;
            case 'method4':
                $now->modify('-15 seconds');
                $expirationMinutes = 20;
                // Use different nonce generation
                $nonceRaw = hash('sha256', uniqid() . microtime(), true);
                $nonce = base64_encode(substr($nonceRaw, 0, 16));
                break;
            default:
                $now->modify('-10 seconds');
                $expirationMinutes = 15;
        }

        $created = $now->format('Y-m-d\TH:i:s\Z');
        $expires = (clone $now)->modify("+{$expirationMinutes} minutes")->format('Y-m-d\TH:i:s\Z');

        // Create password digest: Base64(SHA1(nonce + created + password))
        $passwordDigest = base64_encode(sha1($nonceRaw . $created . $this->authKey, true));

        return '<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope
    xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
    xmlns:v3="http://ec.europa.eu/sanco/tracesnt/base/v3"
    xmlns:euimport="http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01"
    xmlns:base="http://ec.europa.eu/sanco/tracesnt/base/v4">
    <soapenv:Header>
        <wsse:Security
            xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"
            xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
            soapenv:mustUnderstand="1">
            <wsse:UsernameToken wsu:Id="UsernameToken-' . uniqid() . '">
                <wsse:Username>' . htmlspecialchars($this->username) . '</wsse:Username>
                <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest">' . $passwordDigest . '</wsse:Password>
                <wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">' . $nonce . '</wsse:Nonce>
                <wsu:Created>' . $created . '</wsu:Created>
            </wsse:UsernameToken>
            <wsu:Timestamp wsu:Id="TS-' . uniqid() . '">
                <wsu:Created>' . $created . '</wsu:Created>
                <wsu:Expires>' . $expires . '</wsu:Expires>
            </wsu:Timestamp>
        </wsse:Security>
        <v3:LanguageCode>en</v3:LanguageCode>
        <v3:WebServiceClientId>' . htmlspecialchars($this->clientId) . '</v3:WebServiceClientId>
    </soapenv:Header>
    <soapenv:Body>
        ' . $body . '
    </soapenv:Body>
</soapenv:Envelope>';
    }
}
